/*
 * CRC16校验算法实现
 * 
 * 提供CRC16-CCITT算法的计算和验证功能
 */

#include "check_crc16.h"
#include <stdbool.h>

/**
 * @brief 计算CRC16校验值
 * 
 * 使用标准CRC16-CCITT算法计算校验值
 * 多项式：0x1021，初始值：0xFFFF
 * 
 * @param data 数据指针
 * @param length 数据长度
 * @return uint16_t CRC16校验值
 */
uint16_t calculate_crc16(const uint8_t *data, size_t length)
{
    uint16_t crc = 0xFFFF;
    
    if (data == NULL || length == 0) {
        return crc;
    }
    
    for (size_t i = 0; i < length; i++) {
        crc ^= (uint16_t)data[i] << 8;
        for (int j = 0; j < 8; j++) {
            if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
            } else {
                crc <<= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证CRC16校验值
 * 
 * 验证数据包的CRC16校验是否正确
 * 
 * @param data 包含CRC16的完整数据包指针
 * @param length 数据包总长度（包括CRC16字段）
 * @param crc_offset CRC16字段在数据包中的偏移位置
 * @param little_endian 是否为小端序（true=小端，false=大端）
 * @return true 校验正确，false 校验失败
 */
bool verify_crc16(const uint8_t *data, size_t length, size_t crc_offset, bool little_endian)
{
    if (data == NULL || length < 2 || crc_offset + 2 > length) {
        return false;
    }
    
    // 提取接收到的CRC16值
    uint16_t received_crc;
    if (little_endian) {
        received_crc = data[crc_offset] | (data[crc_offset + 1] << 8);
    } else {
        received_crc = (data[crc_offset] << 8) | data[crc_offset + 1];
    }
    
    // 计算数据的CRC16（不包括CRC字段）
    uint16_t calculated_crc = calculate_crc16(data, crc_offset);
    
    return (received_crc == calculated_crc);
}

/**
 * @brief 添加CRC16校验值到数据包
 * 
 * 计算数据的CRC16并添加到指定位置
 * 
 * @param data 数据包指针
 * @param data_length 数据长度（不包括CRC16字段）
 * @param crc_offset CRC16字段在数据包中的偏移位置
 * @param little_endian 是否为小端序（true=小端，false=大端）
 */
void append_crc16(uint8_t *data, size_t data_length, size_t crc_offset, bool little_endian)
{
    if (data == NULL || crc_offset < data_length) {
        return;
    }
    
    // 计算CRC16
    uint16_t crc = calculate_crc16(data, data_length);
    
    // 根据字节序写入CRC16
    if (little_endian) {
        data[crc_offset] = crc & 0xFF;
        data[crc_offset + 1] = (crc >> 8) & 0xFF;
    } else {
        data[crc_offset] = (crc >> 8) & 0xFF;
        data[crc_offset + 1] = crc & 0xFF;
    }
}
