# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
#
# 8MB Flash 分区表优化方案
# 固件大小约637KB，预留增长空间到1.5MB
#
# 分区布局说明：
# 0x0000   - 0x8000   : 二级引导程序 (32KB)
# 0x8000   - 0x9000   : 分区表 (4KB)
# 0x9000   - 0xF000   : NVS (24KB) - 增加到24KB用于WiFi配置等
# 0xF000   - 0x10000  : PHY初始化数据 (4KB)
# 0x10000  - 0x190000 : OTA_0 应用分区 (1.5MB)
# 0x190000 - 0x310000 : OTA_1 应用分区 (1.5MB)
# 0x310000 - 0x312000 : OTA数据分区 (8KB)
# 0x312000 - 0x712000 : SPIFFS存储分区 (4MB) - 大幅增加存储空间
# 0x712000 - 0x800000 : 预留空间 (952KB) - 可用于未来扩展

nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
ota_0,    app,  ota_0,   0x10000, 0x180000,
ota_1,    app,  ota_1,   0x190000, 0x180000,
otadata,  data, ota,     0x310000, 0x2000,
storage,  data, spiffs,  0x312000, 0x400000,
